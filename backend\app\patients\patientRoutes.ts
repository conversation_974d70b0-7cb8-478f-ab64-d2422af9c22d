/**
 * patientRoutes.ts
 * Express routes for managing Patient resources via Medplum.
 */
import express from 'express';
import { MedplumService } from '../clients/medplum_client';

const router = express.Router();
const medplum = new MedplumService();

/**
 * GET /api/patients/:id
 * Fetch a single patient by ID from Medplum.
 * @param req.params.id - The patient ID.
 * @returns {Object} Patient resource or 404 if not found.
 */
router.get('/:id', async (req, res) => {
  try {
    const patient = await medplum.read('Patient', req.params.id);
    res.json(patient);
  } catch (e) {
    res.status(404).json({ error: 'Patient not found' });
  }
});

/**
 * GET /api/patients
 * Fetch all patients, optionally filtered by doctorId.
 * @param req.query.doctorId - (Optional) The doctor's Practitioner ID.
 * @returns {Array} List of patient summaries.
 */
router.get('/', async (req, res) => {
  try {
    const { doctorId } = req.query;
    let searchParams: Record<string, string | number> = {};
    if (doctorId) {
      searchParams['general-practitioner'] = `Practitioner/${doctorId}`;
    }
    const result = await medplum.search_patient('Patient', searchParams);
    const patients = (result.entry || []).map((e: any) => {
      const p = e.resource;
      let age = null;
      if (p.birthDate) {
        const birth = new Date(p.birthDate);
        const today = new Date();
        age = today.getFullYear() - birth.getFullYear();
        const m = today.getMonth() - birth.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
          age--;
        }
      }
      return {
        id: p.id,
        name: p.name?.[0] ? `${p.name[0].given?.join(' ')} ${p.name[0].family}` : 'Unknown',
        gender: p.gender,
        birthDate: p.birthDate,
        age: age,
        status: 'active',
        metrics: {
          ccsClass: { current: 'N/A' },
          lvef: { current: 'N/A' },
          anginaEpisodes: { current: 'N/A' },
          bp: { current: 'N/A' }
        },
        summary: 'No summary available.'
      };
    });
    res.json(patients);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch patients' });
  }
});

export default router;