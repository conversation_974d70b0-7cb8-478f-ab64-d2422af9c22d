/**
 * test.ts
 * Script for creating test patients in Medplum for different doctors.
 */

import { MedplumService } from '../clients/medplum_client';

/**
 * Main function to create test patients for two doctors in Medplum.
 * Creates 3 patients for the main doctor and 2 for another doctor.
 * @returns {Promise<void>}
 */
async function main(): Promise<void> {
  const medplum = new MedplumService();

  // Main doctor
  const doctorId = '0197affd-c0a5-726e-9b76-fa5ba58164e4';
  // Another doctor
  const otherDoctorId = '0197a386-5f2f-7656-bbc5-53eb70cc5716';

  // Create 3 patients for main doctor
  for (let i = 1; i <= 3; i++) {
    const patient = await medplum.create('Patient', {
      resourceType: 'Patient',
      name: [{ given: [`Test${i}`], family: 'Patient' }],
      gender: i % 2 === 0 ? 'female' : 'male',
      birthDate: `1990-01-0${i}`,
      generalPractitioner: [{ reference: `Practitioner/${doctorId}` }],
    });
    console.log('Created Patient for main doctor:', patient.id, patient.name?.[0]);
  }

  // Create 2 patients for other doctor
  for (let i = 4; i <= 5; i++) {
    const patient = await medplum.create('Patient', {
      resourceType: 'Patient',
      name: [{ given: [`Test${i}`], family: 'Patient' }],
      gender: i % 2 === 0 ? 'female' : 'male',
      birthDate: `1990-01-0${i}`,
      generalPractitioner: [{ reference: `Practitioner/${otherDoctorId}` }],
    });
    console.log('Created Patient for other doctor:', patient.id, patient.name?.[0]);
  }
}

main().catch(console.error);

