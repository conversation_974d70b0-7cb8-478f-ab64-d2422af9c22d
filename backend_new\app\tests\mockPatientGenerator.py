"""
Mock Patient Data Generator for EECP Database
Creates American patient <PERSON> with complete EECP treatment data
Uses existing Supabase connection configuration
"""

from supabase_setup import SupabaseConnection
import json
from datetime import datetime, date

class MockPatientGenerator:
    def __init__(self):
        self.db = SupabaseConnection()
        
    def create_patient(self):

        # Connect to database
        if not self.db.connect():
            print("Failed to connect to database")
            return False
            
        try:
            # 1. Create Clinic
            clinic_data = self.get_clinic_data()
            self.insert_clinic(clinic_data)
            
            # 2. Create Doctor
            doctor_data = self.get_doctor_data()
            self.insert_doctor(doctor_data)
            
            # 3. Associate Doctor with Clinic
            association_data = self.get_doctor_clinic_association()
            self.insert_doctor_clinic_association(association_data)
            
            # 4. Create Patient
            patient_data = self.get_patient_data()
            self.insert_patient(patient_data)
            
            # 5. Create Medical Records
            medical_record = self.get_medical_record_data()
            self.insert_medical_record(medical_record)
            
            # 6. Create Intake Notes
            intake_notes = self.get_intake_notes_data()
            self.insert_intake_notes(intake_notes)
            
            # 7. Create Medications
            medications = self.get_medications_data()
            for medication in medications:
                self.insert_medication(medication)
            
            # 8. Create EECP Sessions
            sessions = self.get_sessions_data()
            for session in sessions:
                self.insert_session(session)
            
            # 9. Create Follow-up Appointment
            appointment = self.get_appointment_data()
            self.insert_appointment(appointment)
            
            print("\nAll patient data created successfully!")
            self.print_patient_summary()
            
            # Export to JSON
            self.export_to_json()
            
            return True
            
        except Exception as e:
            print(f"Error creating patient data: {e}")
            return False
        finally:
            self.db.close()
    
    def get_clinic_data(self):
        """Dallas Heart Care Center data"""
        return {
            'clinic_id': 'CLINIC_001',
            'clinic_name': 'Heart Care Center Dallas',
            'address': {
                "street": "5500 Medical Plaza Drive, Suite 300",
                "city": "Dallas", 
                "state": "Texas",
                "zip_code": "75235",
                "country": "United States"
            },
            'phone': '******-555-0123',
            'email': '<EMAIL>',
            'website': 'https://heartcaredallas.com',
            'operating_hours': {
                "monday": "07:00-17:00",
                "tuesday": "07:00-17:00", 
                "wednesday": "07:00-17:00",
                "thursday": "07:00-17:00",
                "friday": "07:00-17:00",
                "saturday": "08:00-12:00",
                "sunday": "closed"
            },
            'timezone': 'America/Chicago',
            'clinic_type': 'cardiac_center',
            'license_number': 'TX-CC-2024-001',
            'eecp_machines_count': 3,
            'max_daily_sessions': 48,
            'treatment_rooms': ['Room A', 'Room B', 'Room C'],
            'director_name': 'Dr. Michael Chen',
            'status': 'active'
        }
    
    def get_doctor_data(self):
        """Dr. Michael Chen data"""
        return {
            'doctor_id': 'DOC_001',
            'first_name': 'Dr. Michael',
            'last_name': 'Chen',
            'email': '<EMAIL>',
            'phone': '******-555-0156',
            'specialization': 'Interventional Cardiology',
            'license_number': 'TX-MD-12345',
            'login_username': 'dr_michael_chen',
            'status': 'active'
        }
    
    def get_doctor_clinic_association(self):
        """Doctor-clinic relationship"""
        return {
            'doctor_id': 'DOC_001',
            'clinic_id': 'CLINIC_001',
            'employment_type': 'full_time',
            'start_date': '2024-01-01',
            'is_primary_clinic': True,
            'permissions': {
                "can_edit_patients": True,
                "can_schedule_appointments": True,
                "can_view_reports": True,
                "can_manage_sessions": True
            }
        }
    
    def get_patient_data(self):
        """Robert Johnson - American patient data"""
        return {
            'patient_id': 'PAT_001',
            'first_name': 'Robert',
            'last_name': 'Johnson',
            'date_of_birth': '1968-07-22',
            'gender': 'Male',
            'height': 72.5,  # 6'0.5" in inches
            'phone': '******-555-7890',
            'email': '<EMAIL>',
            'address': {
                "street": "1425 Oak Tree Lane",
                "city": "Plano",
                "state": "Texas", 
                "zip_code": "75024",
                "country": "United States"
            },
            'emergency_contact': {
                "name": "Susan Johnson",
                "relationship": "Wife",
                "phone": "******-555-7891"
            },
            'preferred_contact_method': 'phone',
            'preferred_contact_time': 'Morning (8AM-12PM)',
            'health_card_number': 'BC123456789',
            'insurance_info': {
                "provider": "Blue Cross Blue Shield of Texas",
                "policy_number": "BCBS123456789",
                "group_number": "ABC123",
                "coverage_amount": "1000000"
            },
            'referral_source': 'Physician Referral',
            'referring_doctor_name': 'Dr. Sarah Williams',
            'referring_institution': 'Dallas Methodist Hospital',
            'referral_notes': 'Patient with refractory angina despite optimal medical therapy and previous PCI',
            'eecp_approved': True,
            'approved_by': 'DOC_001',
            'approval_date': '2024-02-01',
            'approval_notes': 'Good candidate for EECP - stable angina, adequate LV function',
            'anticipated_treatment_plan': '35 EECP sessions over 7 weeks',
            'status': 'active'
        }
    
    def get_medical_record_data(self):
        """Robert Johnson's medical records"""
        return {
            'patient_id': 'PAT_001',
            'from_date': '2024-02-01T00:00:00+00:00',
            'primary_diagnosis': 'Chronic Stable Angina Pectoris',
            'secondary_diagnoses': ['Essential Hypertension', 'Type 2 Diabetes Mellitus', 'Hyperlipidemia'],
            'lvef': 48,
            'bnp': 285,
            'nyha_class': 'II',
            'total_sessions': 35,
            'sessions_completed': 15,
            'start_date': '2024-02-15',
            'expected_completion': '2024-05-02',
            'medical_history': {
                "previous_mi": "2023-08-12",
                "previous_procedures": ["PCI to RCA with DES 2023-09-01"],
                "family_history": "Father deceased from MI at age 62",
                "smoking_history": "Former smoker, quit 2022 (20 pack-year history)",
                "alcohol_history": "Social drinking, 2-3 drinks per week"
            },
            'current_medications': {
                "antiplatelet": [
                    {"name": "Aspirin", "dose": "81mg", "frequency": "once daily"},
                    {"name": "Clopidogrel", "dose": "75mg", "frequency": "once daily"}
                ],
                "cardiac": [
                    {"name": "Metoprolol XL", "dose": "100mg", "frequency": "once daily"},
                    {"name": "Lisinopril", "dose": "10mg", "frequency": "once daily"}
                ],
                "lipid": [
                    {"name": "Atorvastatin", "dose": "80mg", "frequency": "once daily at bedtime"}
                ],
                "diabetes": [
                    {"name": "Metformin XR", "dose": "1000mg", "frequency": "twice daily"}
                ]
            },
            'allergies': ['Penicillin (rash)', 'Sulfa drugs (GI upset)'],
            'treating_doctor_id': 'DOC_001',
            'patient_summary_soap': {
                "subjective": "56-year-old male reports improved exercise tolerance after 15 EECP sessions. Chest pain episodes reduced from daily to 1-2 times per week. Can now walk 2 blocks before onset of chest discomfort.",
                "objective": "BP: 132/78, HR: 64 bpm regular, O2 sat 98% RA. No acute distress, clear lungs, regular S1/S2, no murmurs. Lower extremities without edema.",
                "assessment": "Excellent response to EECP therapy. Significant reduction in anginal symptoms and improved functional capacity. Vitals stable.",
                "plan": "Continue EECP sessions as planned. Monitor BP and blood glucose. Follow-up echo and stress test after completion. Consider cardiac rehabilitation enrollment."
            }
        }
    
    def get_intake_notes_data(self):
        """Initial patient assessment"""
        return {
            'patient_id': 'PAT_001',
            'intake_date': '2024-02-01',
            'intake_doctor_id': 'DOC_001',
            'chief_complaint': 'Chest pain with minimal exertion limiting daily activities',
            'history_of_present_illness': 'Mr. Johnson is a 56-year-old male with CAD s/p MI and PCI presenting with recurrent chest pain on minimal exertion despite optimal medical therapy. Describes substernal chest pressure 6/10 that radiates to left arm, occurs with walking 1 block, relieved with rest in 2-3 minutes.',
            'review_of_systems': {
                "cardiovascular": "Chest pain on exertion, no palpitations, no syncope, no orthopnea",
                "respiratory": "No dyspnea at rest, mild SOB with exertion concurrent with chest pain",
                "gastrointestinal": "No nausea, vomiting, or heartburn with episodes",
                "neurological": "No dizziness, no headaches, no focal weakness",
                "endocrine": "Well-controlled diabetes, monitors blood sugar twice daily"
            },
            'physical_examination_notes': 'Vitals: BP 138/82, HR 68, RR 16, T 98.4°F, O2 sat 99% RA. General: Alert, cooperative, well-appearing male in NAD. CV: RRR, normal S1/S2, no murmurs/rubs/gallops. Lungs: CTAB, no wheezes/rales/rhonchi. Extremities: No pedal edema, distal pulses intact.',
            'assessment_and_plan': 'Patient is an excellent candidate for EECP given refractory angina despite optimal medical therapy and previous revascularization. LVEF adequate for treatment.',
            'eecp_candidacy_assessment': 'Excellent candidate - CCS Class II angina, LVEF >40%, no contraindications identified',
            'contraindications_noted': [],
            'risks_benefits_discussed': True,
            'patient_consent_obtained': True
        }
    
    def get_medications_data(self):
        """Current medications list"""
        return [
            {
                'patient_id': 'PAT_001',
                'medication_name': 'Metoprolol XL',
                'dosage': '100mg',
                'frequency': 'Once daily',
                'route': 'Oral',
                'indication': 'Hypertension, Coronary Artery Disease',
                'prescriber': 'Dr. Michael Chen',
                'prescribing_doctor_id': 'DOC_001',
                'start_date': '2024-02-01',
                'status': 'active',
                'instructions': 'Take in the morning with food'
            },
            {
                'patient_id': 'PAT_001',
                'medication_name': 'Atorvastatin',
                'dosage': '80mg',
                'frequency': 'Once daily at bedtime',
                'route': 'Oral',
                'indication': 'Hyperlipidemia',
                'prescriber': 'Dr. Michael Chen',
                'prescribing_doctor_id': 'DOC_001',
                'start_date': '2024-02-01',
                'status': 'active',
                'instructions': 'Take at bedtime, avoid grapefruit juice'
            },
            {
                'patient_id': 'PAT_001',
                'medication_name': 'Metformin XR',
                'dosage': '1000mg',
                'frequency': 'Twice daily',
                'route': 'Oral',
                'indication': 'Type 2 Diabetes Mellitus',
                'prescriber': 'Dr. Michael Chen',
                'prescribing_doctor_id': 'DOC_001',
                'start_date': '2024-02-01',
                'status': 'active',
                'instructions': 'Take with meals to reduce GI upset'
            },
            {
                'patient_id': 'PAT_001',
                'medication_name': 'Aspirin',
                'dosage': '81mg',
                'frequency': 'Once daily',
                'route': 'Oral',
                'indication': 'Antiplatelet therapy',
                'prescriber': 'Dr. Michael Chen',
                'prescribing_doctor_id': 'DOC_001',
                'start_date': '2024-02-01',
                'status': 'active',
                'instructions': 'Take with food to prevent stomach irritation'
            },
            {
                'patient_id': 'PAT_001',
                'medication_name': 'Clopidogrel',
                'dosage': '75mg',
                'frequency': 'Once daily',
                'route': 'Oral',
                'indication': 'Antiplatelet therapy',
                'prescriber': 'Dr. Michael Chen',
                'prescribing_doctor_id': 'DOC_001',
                'start_date': '2024-02-01',
                'status': 'active',
                'instructions': 'May take with or without food'
            },
            {
                'patient_id': 'PAT_001',
                'medication_name': 'Lisinopril',
                'dosage': '10mg',
                'frequency': 'Once daily',
                'route': 'Oral',
                'indication': 'Hypertension, Cardioprotection',
                'prescriber': 'Dr. Michael Chen',
                'prescribing_doctor_id': 'DOC_001',
                'start_date': '2024-02-01',
                'status': 'active',
                'instructions': 'Monitor blood pressure regularly, report persistent cough'
            }
        ]
    
    def get_sessions_data(self):
        """Sample EECP sessions showing treatment progression"""
        return [
            # Session 1 - First treatment
            {
                'patient_id': 'PAT_001',
                'session_number': 1,
                'session_date': '2024-02-15',
                'start_time': '08:00:00',
                'end_time': '09:00:00',
                'duration_minutes': 60,
                'status': 'completed',
                'room': 'Room A',
                'therapist_name': 'Jennifer Martinez, RN',
                'physician_name': 'Dr. Michael Chen',
                'attending_doctor_id': 'DOC_001',
                'clinic_id': 'CLINIC_001',
                'pre_measurements': {
                    "systolic_bp": 138,
                    "diastolic_bp": 82,
                    "heart_rate": 68,
                    "weight": 185.2,
                    "pain_level": 4,
                    "o2_saturation": 99
                },
                'during_15min_measurements': {
                    "systolic_bp": 132,
                    "diastolic_bp": 78,
                    "heart_rate": 64,
                    "pain_level": 2
                },
                'during_45min_measurements': {
                    "systolic_bp": 128,
                    "diastolic_bp": 76,
                    "heart_rate": 62,
                    "pain_level": 1
                },
                'post_measurements': {
                    "systolic_bp": 125,
                    "diastolic_bp": 74,
                    "heart_rate": 60,
                    "pain_level": 0
                },
                'physician_evaluation': 'Patient tolerated first EECP session very well. Excellent hemodynamic response with significant symptom improvement during treatment.',
                'session_notes': 'First EECP session completed successfully. Patient experienced immediate chest pain relief during treatment. Tolerated 300mmHg cuff pressure well. No adverse events.',
                'adverse_events': [],
                'session_soap_notes': {
                    "subjective": "Patient reports immediate chest pain relief during treatment, feels energized",
                    "objective": "Stable vitals throughout treatment, good cuff tolerance, no distress",
                    "assessment": "Excellent response to initial EECP session",
                    "plan": "Continue treatment as scheduled, monitor BP and symptoms"
                }
            },
            # Session 15 - Mid-treatment
            {
                'patient_id': 'PAT_001',
                'session_number': 15,
                'session_date': '2024-03-20',
                'start_time': '08:00:00',
                'end_time': '09:00:00',
                'duration_minutes': 60,
                'status': 'completed',
                'room': 'Room A',
                'therapist_name': 'Jennifer Martinez, RN',
                'physician_name': 'Dr. Michael Chen',
                'attending_doctor_id': 'DOC_001',
                'clinic_id': 'CLINIC_001',
                'pre_measurements': {
                    "systolic_bp": 132,
                    "diastolic_bp": 78,
                    "heart_rate": 64,
                    "weight": 182.8,
                    "pain_level": 2,
                    "o2_saturation": 99
                },
                'during_15min_measurements': {
                    "systolic_bp": 126,
                    "diastolic_bp": 74,
                    "heart_rate": 60,
                    "pain_level": 0
                },
                'during_45min_measurements': {
                    "systolic_bp": 122,
                    "diastolic_bp": 72,
                    "heart_rate": 58,
                    "pain_level": 0
                },
                'post_measurements': {
                    "systolic_bp": 120,
                    "diastolic_bp": 70,
                    "heart_rate": 56,
                    "pain_level": 0
                },
                'physician_evaluation': 'Excellent progress at midpoint of treatment. Patient reports significant improvement in exercise tolerance and quality of life.',
                'session_notes': 'Mid-treatment assessment shows remarkable improvement. Patient can now walk 2 blocks without chest pain. Weight loss of 2.4 lbs noted.',
                'adverse_events': [],
                'session_soap_notes': {
                    "subjective": "Significant improvement in exercise tolerance, walking 2 blocks without chest pain, better sleep quality",
                    "objective": "Improved hemodynamics, weight loss noted, excellent treatment tolerance",
                    "assessment": "Outstanding response to EECP at midpoint evaluation",
                    "plan": "Continue treatment course, encourage gradual activity increase"
                }
            },
            # Session 35 - Final treatment
            {
                'patient_id': 'PAT_001',
                'session_number': 35,
                'session_date': '2024-05-02',
                'start_time': '08:00:00',
                'end_time': '09:00:00',
                'duration_minutes': 60,
                'status': 'completed',
                'room': 'Room A',
                'therapist_name': 'Jennifer Martinez, RN',
                'physician_name': 'Dr. Michael Chen',
                'attending_doctor_id': 'DOC_001',
                'clinic_id': 'CLINIC_001',
                'pre_measurements': {
                    "systolic_bp": 128,
                    "diastolic_bp": 76,
                    "heart_rate": 62,
                    "weight": 179.5,
                    "pain_level": 0,
                    "o2_saturation": 99
                },
                'during_15min_measurements': {
                    "systolic_bp": 122,
                    "diastolic_bp": 72,
                    "heart_rate": 58,
                    "pain_level": 0
                },
                'during_45min_measurements': {
                    "systolic_bp": 118,
                    "diastolic_bp": 70,
                    "heart_rate": 56,
                    "pain_level": 0
                },
                'post_measurements': {
                    "systolic_bp": 116,
                    "diastolic_bp": 68,
                    "heart_rate": 54,
                    "pain_level": 0
                },
                'physician_evaluation': 'Outstanding completion of EECP course. Patient achieved excellent clinical outcomes with complete resolution of rest angina and marked improvement in functional capacity.',
                'session_notes': 'Final EECP session completed with exceptional results. Total weight loss 5.7 lbs. Patient can now walk >4 blocks and climb 2 flights of stairs without chest pain. Plans to join cardiac rehabilitation program.',
                'adverse_events': [],
                'session_soap_notes': {
                    "subjective": "Complete resolution of rest angina, can walk >4 blocks and climb stairs without chest pain, feels like a new person",
                    "objective": "Optimal vital signs, significant weight loss, excellent exercise tolerance demonstrated",
                    "assessment": "Highly successful completion of EECP course with outstanding clinical outcomes",
                    "plan": "Graduate to cardiac rehabilitation, continue medications, follow-up in 4 weeks with stress echo"
                }
            }
        ]
    
    def get_appointment_data(self):
        """Follow-up appointment"""
        return {
            'patient_id': 'PAT_001',
            'appointment_date': '2024-05-30T10:00:00-05:00',
            'appointment_type': 'follow_up',
            'status': 'scheduled',
            'notes': 'Post-EECP follow-up consultation with stress echocardiogram and functional assessment',
            'assigned_doctor_id': 'DOC_001',
            'clinic_id': 'CLINIC_001'
        }
    
    # Database insert methods using your Supabase client
    def insert_clinic(self, data):
        response = self.db.client.table('clinics').insert(data).execute()
        print(f" Clinic '{data['clinic_name']}' created")
        return response.data[0] if response.data else None
    
    def insert_doctor(self, data):
        response = self.db.client.table('doctors').insert(data).execute()
        print(f" Doctor '{data['first_name']} {data['last_name']}' created")
        return response.data[0] if response.data else None
    
    def insert_doctor_clinic_association(self, data):
        response = self.db.client.table('doctor_clinic_associations').insert(data).execute()
        print(" Doctor-clinic association created")
        return response.data[0] if response.data else None
    
    def insert_patient(self, data):
        response = self.db.client.table('patients').insert(data).execute()
        print(f" Patient '{data['first_name']} {data['last_name']}' created")
        return response.data[0] if response.data else None
    
    def insert_medical_record(self, data):
        response = self.db.client.table('patient_medical_records').insert(data).execute()
        print(" Medical record created")
        return response.data[0] if response.data else None
    
    def insert_intake_notes(self, data):
        response = self.db.client.table('patient_intake_notes').insert(data).execute()
        print(" Intake notes created")
        return response.data[0] if response.data else None
    
    def insert_medication(self, data):
        response = self.db.client.table('patients_medications').insert(data).execute()
        print(f" Medication '{data['medication_name']}' added")
        return response.data[0] if response.data else None
    
    def insert_session(self, data):
        response = self.db.client.table('eecp_sessions').insert(data).execute()
        print(f" EECP Session {data['session_number']} created")
        return response.data[0] if response.data else None
    
    def insert_appointment(self, data):
        response = self.db.client.table('appointments').insert(data).execute()
        print(" Follow-up appointment scheduled")
        return response.data[0] if response.data else None
    
    def print_patient_summary(self):
        """Print summary of created patient"""
        print("\n Patient Summary:")
        print(" Robert Johnson (PAT_001)")
        print(" Plano, Texas, USA")
        print(" Heart Care Center Dallas")
        print(" Dr. Michael Chen")
        print(" 6 Medications")
        print(" 3 EECP Sessions (1st, 15th, 35th)")
        print(" Follow-up appointment scheduled")
        print(" Complete treatment success story")
    
    def export_to_json(self):
        """Export all data to JSON file"""
        all_data = {
            'clinic': self.get_clinic_data(),
            'doctor': self.get_doctor_data(),
            'doctor_clinic_association': self.get_doctor_clinic_association(),
            'patient': self.get_patient_data(),
            'medical_record': self.get_medical_record_data(),
            'intake_notes': self.get_intake_notes_data(),
            'medications': self.get_medications_data(),
            'sessions': self.get_sessions_data(),
            'appointment': self.get_appointment_data()
        }
        
        # Save to JSON file
        with open('robert_johnson_patient_data.json', 'w') as f:
            json.dump(all_data, f, indent=2, default=str)
        
        print("Data exported to: robert_johnson_patient_data.json")

def main():
    """Run the mock patient generator"""
    generator = MockPatientGenerator()
    success = generator.create_patient()
    
    if success:
        print("\nRobert Johnson patient data created successfully!")
        print("Ready for EECP treatment demonstration!")
    else:
        print("\nFailed to create patient data")

if __name__ == "__main__":
    main()