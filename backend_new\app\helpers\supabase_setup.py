# Test file for connecting to supabase will be here.
import psycopg2
from dotenv import load_dotenv
import os
from datetime import datetime

# Load environment variables from .env
load_dotenv()

# Fetch database credentials
USER = os.getenv("user")
PASSWORD = os.getenv("password")
HOST = os.getenv("host")
PORT = os.getenv("port")
DBNAME = os.getenv("dbname")

class SupabaseConnection:
    def __init__(self):
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """Establish connection to Supabase PostgreSQL database"""
        try:
            self.connection = psycopg2.connect(
                user=USER,
                password=PASSWORD,
                host=HOST,
                port=PORT,
                dbname=DBNAME
            )
            # Create standard cursor
            self.cursor = self.connection.cursor()
            print("Connection to Supabase successful!")
            return True
        except Exception as e:
            print(f"Failed to connect to Supabase: {e}")
            return False
    
    def create_test_table(self):
        """Create a test table if it doesn't exist"""
        try:
            create_table_query = """
            CREATE TABLE IF NOT EXISTS test_messages (
                id SERIAL PRIMARY KEY,
                message TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            self.cursor.execute(create_table_query)
            self.connection.commit()
            print("Test table created/verified successfully!")
            return True
        except Exception as e:
            print(f"Failed to create table: {e}")
            self.connection.rollback()
            return False
    
    def write_message(self, message):
        """Write a message to the database"""
        try:
            insert_query = """
            INSERT INTO test_messages (message, created_at)
            VALUES (%s, %s)
            RETURNING id, message, created_at;
            """
            timestamp = datetime.now()
            self.cursor.execute(insert_query, (message, timestamp))
            
            # Get the inserted record (returns tuple)
            inserted_record = self.cursor.fetchone()
            self.connection.commit()
            
            print(f"Successfully wrote to database!")
            print(f"ID: {inserted_record[0]}")
            print(f"Message: {inserted_record[1]}")
            print(f"Created at: {inserted_record[2]}")
            
            return inserted_record
        except Exception as e:
            print(f"Failed to write message: {e}")
            self.connection.rollback()
            return None
    
    def read_all_messages(self):
        """Read all messages from the database"""
        try:
            select_query = "SELECT * FROM test_messages ORDER BY created_at DESC;"
            self.cursor.execute(select_query)
            messages = self.cursor.fetchall()
            
            print(f"\nAll messages in database ({len(messages)} total):")
            for msg in messages:
                # msg is a tuple: (id, message, created_at)
                print(f"   [{msg[0]}] {msg[1]} - {msg[2]}")
            
            return messages
        except Exception as e:
            print(f"Failed to read messages: {e}")
            return []
    
    def close(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("Connection closed.")

def main():
    # Initialize connection
    db = SupabaseConnection()
    
    # Connect to Supabase
    if not db.connect():
        return
    
    # Create test table
    if not db.create_test_table():
        db.close()
        return
    
    # Write a test message
    test_message = f"Hello from Python! Test write at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    db.write_message(test_message)
    
    # Write another message
    db.write_message("This is a second test message to Supabase!")
    
    # Read all messages
    db.read_all_messages()
    
    # Close connection
    db.close()

if __name__ == "__main__":
    main()