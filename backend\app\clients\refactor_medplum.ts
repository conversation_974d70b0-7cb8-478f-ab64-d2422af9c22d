import dotenv from 'dotenv';
import { Resource } from '@medplum/fhirtypes';
import { MedplumClient, ClientStorage} from '@medplum/core';
dotenv.config();

/**
 * MedplumService handles Medplum FHIR API authentication and resource operations.
 */
export class MedplumHelper {

    private clientId: string = process.env.CLIENT_ID || ''
    private clientSecret: string = process.env.CLIENT_SECRET || ''
    // private baseUrl: string = 'https://api.medplum.com/fhir/R4'

    private medplumHelper = new MedplumClient({
        baseUrl: 'https://api.medplum.com',
        storage: new ClientStorage(),
        clientId: this.clientId,
        clientSecret: this.clientSecret,
      });

    async initializeMedplum() {
        console.log(this.clientId, this.clientSecret);
        
        await this.medplumHelper.startClientLogin(
            this.clientId,
            this.clientSecret
        );
    }

    async create(patient: Resource) {
        const created_patient = await this.medplumHelper.createResource(patient);
        return created_patient;
    }

    async update(resource: Resource) {
        const updated_resource = await this.medplumHelper.updateResource(resource);
        return updated_resource;
    }

}