import { MedplumHelper } from '../clients/refactor_medplum';
import { Practitioner } from '@medplum/fhirtypes';


async function testPatientFunctions() {
    const medplum = new MedplumHelper();
    await medplum.initializeMedplum();

    const practitioner: Practitioner = {
        resourceType: 'Practitioner',
        name: [{ family: '<PERSON>', given: ['<PERSON>'], prefix: ['Dr.'] }],
    }

    const created_patient = await medplum.create(practitioner);
    console.log(created_patient);

}

testPatientFunctions();

// Running the test.
// npx tsx testPatientFunctions.ts

