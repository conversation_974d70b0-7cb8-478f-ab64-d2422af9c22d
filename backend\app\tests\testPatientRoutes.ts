import express from "express";
import cors    from "cors";
import dotenv  from "dotenv";

import authRoutes from '../auth/loginRoutes';
import patientRoutes from '../patients/patientRoutes'

dotenv.config();

function initServer() {
    const app = express();
    const port = process.env.PORT || 4000;
  
    app.use(cors());
    app.use(express.json());
  
    // Routes
    app.use('/auth', authRoutes);
    app.use('/api/patients', patientRoutes);
  
    app.listen(port, () => {
      console.log(`🚀 Server listening at http://localhost:${port}`);
    });
}

async function testPatientRoutes() {
    // Initialize the server
    initServer();

    // Retrieve a patient by an existingID
    const response = await fetch('http://localhost:4000/api/patients/0197e1e4-435c-77fd-b2f2-ef2cddda1a9e');
    const data = await response.json();
    console.log(data);
}

// Running the test

