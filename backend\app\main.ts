import express from "express";
import cors    from "cors";
import dotenv  from "dotenv";

import authRoutes from './auth/loginRoutes';
import patientRoutes from './patients/patientRoutes';

dotenv.config();

/**
 * main.ts
 * Main function for starting server and running all routes.
 */
async function main() {
  const app = express();
  const port = process.env.PORT || 4000;

  app.use(cors());
  app.use(express.json());

  // Routes
  app.use('/auth', authRoutes);
  app.use('/api/patients', patientRoutes);

  app.listen(port, () => {
    console.log(`🚀 Server listening at http://localhost:${port}`);
  });
}

main().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

// Next Connect this to the frontend.

